# Utilidades Auxiliares del Sistema Revisor-Rmd
# =============================================

# Cargar librerías necesarias
suppressPackageStartupMessages({
  library(yaml)
  library(stringr)
  library(tools)
  library(digest)
})

# ============================================================================
# CONFIGURACIÓN Y LOGGING
# ============================================================================

#' Cargar configuración desde archivo YAML
#' @param config_file Ruta al archivo de configuración
#' @return Lista con la configuración cargada
load_config <- function(config_file = "config/main.yaml") {
  if (!file.exists(config_file)) {
    stop(paste("Archivo de configuración no encontrado:", config_file))
  }
  
  tryCatch({
    config <- yaml::read_yaml(config_file)
    
    # Validar configuración básica
    validate_config(config)
    
    return(config)
  }, error = function(e) {
    stop(paste("Error al cargar configuración:", e$message))
  })
}

#' Validar estructura de configuración
#' @param config Lista de configuración
validate_config <- function(config) {
  required_sections <- c("validation", "output", "directories")
  
  for (section in required_sections) {
    if (!section %in% names(config)) {
      stop(paste("Sección requerida faltante en configuración:", section))
    }
  }
  
  # Validar directorios
  if (!is.null(config$directories$output)) {
    dir.create(config$directories$output, showWarnings = FALSE, recursive = TRUE)
  }
  
  if (!is.null(config$directories$logs)) {
    dir.create(config$directories$logs, showWarnings = FALSE, recursive = TRUE)
  }
}

#' Configurar sistema de logging
#' @param config Lista de configuración
setup_logging <- function(config) {
  log_level <- config$logging$level %||% "INFO"
  log_file <- config$logging$file %||% "logs/revisor-rmd.log"
  
  # Crear directorio de logs si no existe
  log_dir <- dirname(log_file)
  dir.create(log_dir, showWarnings = FALSE, recursive = TRUE)
  
  # Configurar logging global
  options(
    revisor.log.level = log_level,
    revisor.log.file = log_file,
    revisor.log.console = config$logging$console %||% TRUE
  )
}

#' Función de logging
#' @param message Mensaje a registrar
#' @param level Nivel de log (DEBUG, INFO, WARNING, ERROR)
#' @param category Categoría del mensaje
log_message <- function(message, level = "INFO", category = "GENERAL") {
  timestamp <- format(Sys.time(), "%Y-%m-%d %H:%M:%S")
  log_entry <- sprintf("[%s] %s [%s] %s", timestamp, level, category, message)
  
  # Log a consola si está habilitado
  if (getOption("revisor.log.console", TRUE)) {
    cat(log_entry, "\n")
  }
  
  # Log a archivo
  log_file <- getOption("revisor.log.file")
  if (!is.null(log_file)) {
    cat(log_entry, "\n", file = log_file, append = TRUE)
  }
}

# ============================================================================
# MANEJO DE ARCHIVOS
# ============================================================================

#' Encontrar archivos .Rmd en directorio
#' @param directory Directorio a buscar
#' @param recursive Búsqueda recursiva
#' @param exclude_patterns Patrones a excluir
#' @return Vector de rutas de archivos .Rmd
find_rmd_files <- function(directory = ".", recursive = TRUE, exclude_patterns = NULL) {
  # Patrones de archivos Rmd
  patterns <- c("*.Rmd", "*.rmd")
  
  files <- c()
  for (pattern in patterns) {
    found <- list.files(directory, pattern = pattern, full.names = TRUE, 
                       recursive = recursive, ignore.case = TRUE)
    files <- c(files, found)
  }
  
  # Eliminar duplicados
  files <- unique(files)
  
  # Aplicar exclusiones
  if (!is.null(exclude_patterns)) {
    for (pattern in exclude_patterns) {
      files <- files[!grepl(pattern, files)]
    }
  }
  
  return(files)
}

#' Leer archivo Rmd con manejo de errores
#' @param file_path Ruta del archivo
#' @param encoding Codificación del archivo
#' @return Contenido del archivo como vector de caracteres
read_rmd_file <- function(file_path, encoding = "UTF-8") {
  if (!file.exists(file_path)) {
    stop(paste("Archivo no encontrado:", file_path))
  }
  
  tryCatch({
    content <- readLines(file_path, encoding = encoding, warn = FALSE)
    return(content)
  }, error = function(e) {
    log_message(paste("Error al leer archivo", file_path, ":", e$message), 
                "ERROR", "FILE_IO")
    return(NULL)
  })
}

#' Crear backup de archivo
#' @param file_path Ruta del archivo original
#' @param backup_dir Directorio de backup
#' @return Ruta del archivo de backup
create_backup <- function(file_path, backup_dir = "backups/") {
  if (!file.exists(file_path)) {
    stop(paste("Archivo no encontrado:", file_path))
  }
  
  # Crear directorio de backup
  dir.create(backup_dir, showWarnings = FALSE, recursive = TRUE)
  
  # Generar nombre de backup con timestamp
  timestamp <- format(Sys.time(), "%Y%m%d_%H%M%S")
  file_name <- basename(file_path)
  backup_name <- paste0(tools::file_path_sans_ext(file_name), "_", 
                       timestamp, ".", tools::file_ext(file_name))
  backup_path <- file.path(backup_dir, backup_name)
  
  # Copiar archivo
  file.copy(file_path, backup_path)
  
  log_message(paste("Backup creado:", backup_path), "INFO", "BACKUP")
  return(backup_path)
}

# ============================================================================
# PARSING DE ARCHIVOS RMD
# ============================================================================

#' Parsear archivo Rmd y extraer componentes
#' @param content Contenido del archivo como vector de caracteres
#' @return Lista con componentes del archivo
parse_rmd_content <- function(content) {
  result <- list(
    yaml_header = NULL,
    r_chunks = list(),
    python_chunks = list(),
    text_content = NULL,
    metadata = list()
  )
  
  # Extraer header YAML
  yaml_start <- which(str_detect(content, "^---\\s*$"))
  if (length(yaml_start) >= 2) {
    yaml_content <- content[(yaml_start[1] + 1):(yaml_start[2] - 1)]
    result$yaml_header <- yaml_content

    # Parsear metadatos
    tryCatch({
      result$metadata <- yaml::yaml.load(paste(yaml_content, collapse = "\n"))
    }, error = function(e) {
      log_message(paste("Error al parsear YAML header:", e$message),
                  "WARNING", "YAML_PARSE")
    })
  }

  # Extraer metadatos en formato Meta-information (exams package)
  meta_info_start <- which(str_detect(content, "^Meta-information\\s*$"))
  if (length(meta_info_start) > 0) {
    # Buscar desde Meta-information hasta el final del archivo
    meta_start <- meta_info_start[1] + 2  # Saltar "Meta-information" y "================"
    meta_content <- content[meta_start:length(content)]

    # Parsear metadatos en formato exams
    for (line in meta_content) {
      if (str_detect(line, "^\\s*$")) next  # Saltar líneas vacías

      # Buscar patrones como "exname: valor"
      if (str_detect(line, "^ex\\w+:\\s*")) {
        parts <- str_split(line, ":\\s*", n = 2)[[1]]
        if (length(parts) == 2) {
          key <- str_trim(parts[1])
          value <- str_trim(parts[2])
          result$metadata[[key]] <- value
        }
      }
    }
  }
  
  # Extraer chunks de código R
  r_chunks <- extract_code_chunks(content, "r")
  result$r_chunks <- r_chunks
  
  # Extraer chunks de código Python
  python_chunks <- extract_code_chunks(content, "python")
  result$python_chunks <- python_chunks
  
  # Extraer contenido de texto (sin chunks)
  text_lines <- content
  # Remover chunks de código
  chunk_pattern <- "^```\\{.*\\}.*$|^```\\s*$"
  in_chunk <- FALSE
  text_only <- c()
  
  for (line in text_lines) {
    if (str_detect(line, "^```\\{")) {
      in_chunk <- TRUE
    } else if (str_detect(line, "^```\\s*$") && in_chunk) {
      in_chunk <- FALSE
    } else if (!in_chunk) {
      text_only <- c(text_only, line)
    }
  }
  
  result$text_content <- text_only
  
  return(result)
}

#' Extraer chunks de código de un tipo específico
#' @param content Contenido del archivo
#' @param language Lenguaje de programación (r, python, etc.)
#' @return Lista de chunks con metadatos
extract_code_chunks <- function(content, language = "r") {
  chunks <- list()
  chunk_pattern <- paste0("^```\\{", language, ".*\\}.*$")
  
  i <- 1
  chunk_counter <- 1
  
  while (i <= length(content)) {
    line <- content[i]
    
    if (str_detect(line, chunk_pattern)) {
      # Inicio de chunk
      chunk_header <- line
      chunk_options <- extract_chunk_options(chunk_header)
      chunk_code <- c()
      
      i <- i + 1
      
      # Leer contenido del chunk
      while (i <= length(content) && !str_detect(content[i], "^```\\s*$")) {
        chunk_code <- c(chunk_code, content[i])
        i <- i + 1
      }
      
      # Crear objeto chunk
      chunk <- list(
        id = chunk_counter,
        language = language,
        options = chunk_options,
        code = chunk_code,
        line_start = i - length(chunk_code) - 1,
        line_end = i
      )
      
      chunks[[chunk_counter]] <- chunk
      chunk_counter <- chunk_counter + 1
    }
    
    i <- i + 1
  }
  
  return(chunks)
}

#' Extraer opciones de chunk de código
#' @param chunk_header Línea de encabezado del chunk
#' @return Lista de opciones
extract_chunk_options <- function(chunk_header) {
  # Extraer contenido entre llaves
  options_str <- str_extract(chunk_header, "\\{.*\\}")
  options_str <- str_remove_all(options_str, "[\\{\\}]")
  
  # Parsear opciones
  options <- list()
  
  if (!is.na(options_str) && nchar(options_str) > 0) {
    # Dividir por comas
    parts <- str_split(options_str, ",")[[1]]
    
    for (part in parts) {
      part <- str_trim(part)
      
      if (str_detect(part, "=")) {
        # Opción con valor
        key_value <- str_split(part, "=", n = 2)[[1]]
        key <- str_trim(key_value[1])
        value <- str_trim(key_value[2])
        
        # Remover comillas si existen
        value <- str_remove_all(value, "[\"\']")
        
        options[[key]] <- value
      } else {
        # Opción booleana o nombre de lenguaje
        if (part %in% c("r", "python", "sql", "bash")) {
          options[["language"]] <- part
        } else {
          options[[part]] <- TRUE
        }
      }
    }
  }
  
  return(options)
}

# ============================================================================
# ESTRUCTURAS DE DATOS Y UTILIDADES
# ============================================================================

#' Operador %||% para valores por defecto
#' @param x Valor a evaluar
#' @param y Valor por defecto si x es NULL
`%||%` <- function(x, y) {
  if (is.null(x)) y else x
}

#' Crear estructura de resultado de validación
#' @param errors Lista de errores
#' @param warnings Lista de advertencias
#' @param info Lista de información
#' @param suggestions Lista de sugerencias
#' @return Estructura estándar de resultado
create_validation_result <- function(errors = list(), warnings = list(), 
                                   info = list(), suggestions = list()) {
  structure(
    list(
      errors = errors,
      warnings = warnings,
      info = info,
      suggestions = suggestions,
      timestamp = Sys.time(),
      summary = list(
        error_count = length(errors),
        warning_count = length(warnings),
        info_count = length(info),
        suggestion_count = length(suggestions)
      )
    ),
    class = "validation_result"
  )
}

#' Combinar múltiples resultados de validación
#' @param ... Resultados de validación a combinar
#' @return Resultado combinado
combine_validation_results <- function(...) {
  results <- list(...)
  
  combined <- create_validation_result()
  
  for (result in results) {
    if (inherits(result, "validation_result")) {
      combined$errors <- c(combined$errors, result$errors)
      combined$warnings <- c(combined$warnings, result$warnings)
      combined$info <- c(combined$info, result$info)
      combined$suggestions <- c(combined$suggestions, result$suggestions)
    }
  }
  
  # Actualizar resumen
  combined$summary <- list(
    error_count = length(combined$errors),
    warning_count = length(combined$warnings),
    info_count = length(combined$info),
    suggestion_count = length(combined$suggestions)
  )
  
  return(combined)
}

#' Generar hash único para contenido
#' @param content Contenido a hashear
#' @return Hash MD5 del contenido
generate_content_hash <- function(content) {
  if (is.character(content)) {
    content_str <- paste(content, collapse = "\n")
  } else {
    content_str <- as.character(content)
  }
  
  return(digest::digest(content_str, algo = "md5"))
}

#' Verificar si un archivo ha cambiado
#' @param file_path Ruta del archivo
#' @param cache_dir Directorio de caché
#' @return TRUE si el archivo ha cambiado
file_has_changed <- function(file_path, cache_dir = ".cache/") {
  if (!file.exists(file_path)) {
    return(TRUE)
  }
  
  # Crear directorio de caché
  dir.create(cache_dir, showWarnings = FALSE, recursive = TRUE)
  
  # Generar nombre de archivo de caché
  cache_file <- file.path(cache_dir, paste0(basename(file_path), ".hash"))
  
  # Calcular hash actual
  current_hash <- tools::md5sum(file_path)
  
  # Comparar con hash guardado
  if (file.exists(cache_file)) {
    saved_hash <- readLines(cache_file, n = 1, warn = FALSE)
    return(current_hash != saved_hash)
  } else {
    # Guardar hash actual
    writeLines(current_hash, cache_file)
    return(TRUE)
  }
}

# ============================================================================
# UTILIDADES DE TEXTO Y FORMATO
# ============================================================================

#' Limpiar y normalizar texto
#' @param text Texto a limpiar
#' @return Texto limpio
clean_text <- function(text) {
  if (is.null(text) || length(text) == 0) {
    return("")
  }
  
  # Convertir a character si es necesario
  text <- as.character(text)
  
  # Remover espacios extra
  text <- str_trim(text)
  text <- str_squish(text)
  
  # Normalizar saltos de línea
  text <- str_replace_all(text, "\r\n", "\n")
  text <- str_replace_all(text, "\r", "\n")
  
  return(text)
}

#' Formatear mensaje de error/warning/info
#' @param message Mensaje
#' @param type Tipo (error, warning, info)
#' @param line_number Número de línea (opcional)
#' @param column Columna (opcional)
#' @return Mensaje formateado
format_message <- function(message, type = "info", line_number = NULL, column = NULL) {
  location <- ""
  if (!is.null(line_number)) {
    location <- paste0(" (línea ", line_number)
    if (!is.null(column)) {
      location <- paste0(location, ", columna ", column)
    }
    location <- paste0(location, ")")
  }
  
  icon <- switch(type,
    "error" = "❌",
    "warning" = "⚠️",
    "info" = "ℹ️",
    "suggestion" = "💡",
    "✓"
  )
  
  return(paste0(icon, " ", message, location))
}

# ============================================================================
# INICIALIZACIÓN
# ============================================================================

# Mensaje de carga
log_message("Utilidades auxiliares cargadas correctamente", "INFO", "INIT")
